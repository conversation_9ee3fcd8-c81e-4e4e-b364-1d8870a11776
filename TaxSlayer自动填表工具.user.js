// ==UserScript==
// @name         TaxSlayer自动填表工具
// @namespace    http://tampermonkey.net/
// @version      3.0
// @description  TaxSlayer全流程自动填写工具，支持完整Tab分隔数据导入和自动点击下一步
// <AUTHOR>
// @match        https://www.taxslayer.com/*
// @grant        GM_setValue
// @grant        GM_getValue
// @grant        GM_deleteValue
// @grant        GM_log
// ==/UserScript==

(function() {
    'use strict';

    // 全局数据存储键名
    const DATA_KEY = 'taxslayer_user_data';

    // 完整数据字段映射
    const DATA_FIELDS = {
        email: 0,           // 邮箱
        username: 1,        // 用户名
        password: 2,        // 密码
        phone: 3,           // 手机号
        smsApi: 4,          // SMS API
        firstName: 5,       // 名字
        lastName: 6,        // 姓氏
        ssn: 7,             // SSN
        birthDate: 8,       // 生日
        address: 9,         // 地址
        zipCode: 10,        // 邮编
        taxId: 11,          // 税务ID
        employer: 12,       // 雇主
        employerAddress: 13, // 雇主地址
        employerZip: 14,    // 雇主邮编
        income1: 15,        // 收入1
        income2: 16,        // 收入2
        income3: 17,        // 收入3
        income4: 18,        // 收入4
        income5: 19,        // 收入5
        income6: 20,        // 收入6
        income7: 21,        // 收入7
        income8: 22,        // 收入8
        income9: 23,        // 收入9
        bankName: 24,       // 银行名称
        routingNumber: 25,  // 路由号
        accountNumber: 26   // 账号
    };

    // 示例数据（用户可以复制使用）
    const SAMPLE_DATA = '<EMAIL>\tMjhjpd952\tYjzbwfnokouz62@\t7572572752\thttps://api.sms8.net/api/record?token=7kbphp7khs5y9cehukt7kg64c4oqk3qep07\tALIASGHAR\tRAHIMIOUN\t861840072\t04/09/1968\t3120 BUENA VISTA DR\t75025\t95-2834236\tLPL FINANCIAL\t4707 EXECUTIVE DRIVE\t92121\t57413\t6315.43\t57413\t3559.606\t57413\t832.4885\t57413\t4305.975\tBarclays Bank Delaware\t031101321\t130013182347';

    // 等待页面加载完成
    function waitForPageLoad() {
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', initTool);
        } else {
            initTool();
        }
    }

    // 初始化工具
    function initTool() {
        // 延迟一秒确保页面完全加载
        setTimeout(() => {
            createImportTool();
            autoFillCurrentPage();
        }, 1000);
    }

    // 创建导入工具面板
    function createImportTool() {
        // 检查是否已经存在工具面板
        if (document.querySelector('#taxslayer-import-tool')) {
            return;
        }

        // 创建导入面板
        const importPanel = document.createElement('div');
        importPanel.id = 'taxslayer-import-tool';
        importPanel.style.cssText = `
            position: fixed;
            top: 10px;
            right: 10px;
            z-index: 9999;
            background: white;
            border: 2px solid #007bff;
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            width: 400px;
            font-family: Arial, sans-serif;
        `;

        // 获取已保存的数据，如果没有则使用示例数据
        const savedData = GM_getValue(DATA_KEY, SAMPLE_DATA);

        importPanel.innerHTML = `
            <h3 style="margin: 0 0 10px 0; color: #007bff;">TaxSlayer资料导入工具 v2.0</h3>
            <p style="margin: 0 0 10px 0; font-size: 12px; color: #666;">
                请粘贴完整的Tab分隔资料（27个字段）：<br>
                邮箱[Tab]用户名[Tab]密码[Tab]手机号[Tab]SMS_API[Tab]名字[Tab]姓氏[Tab]SSN[Tab]生日[Tab]地址[Tab]邮编[Tab]税务ID[Tab]雇主[Tab]雇主地址[Tab]雇主邮编[Tab]收入数据...[Tab]银行信息
            </p>
            <textarea id="dataInput" placeholder="在此粘贴您的完整资料..." style="
                width: 100%;
                height: 100px;
                border: 1px solid #ddd;
                border-radius: 4px;
                padding: 8px;
                font-size: 11px;
                resize: vertical;
                box-sizing: border-box;
            ">${savedData}</textarea>
            <div style="margin-top: 10px;">
                <button id="importBtn" style="
                    background: #28a745;
                    color: white;
                    border: none;
                    padding: 8px 12px;
                    border-radius: 4px;
                    cursor: pointer;
                    margin-right: 3px;
                    font-size: 12px;
                ">保存并导入</button>
                <button id="autoFillBtn" style="
                    background: #17a2b8;
                    color: white;
                    border: none;
                    padding: 8px 12px;
                    border-radius: 4px;
                    cursor: pointer;
                    margin-right: 3px;
                    font-size: 12px;
                ">自动填写</button>
                <button id="autoProcessBtn" style="
                    background: #fd7e14;
                    color: white;
                    border: none;
                    padding: 8px 12px;
                    border-radius: 4px;
                    cursor: pointer;
                    margin-right: 3px;
                    font-size: 12px;
                ">自动流程</button>
                <button id="loadSampleBtn" style="
                    background: #6f42c1;
                    color: white;
                    border: none;
                    padding: 8px 12px;
                    border-radius: 4px;
                    cursor: pointer;
                    margin-right: 3px;
                    font-size: 12px;
                ">示例数据</button>
                <button id="clearBtn" style="
                    background: #ffc107;
                    color: black;
                    border: none;
                    padding: 8px 12px;
                    border-radius: 4px;
                    cursor: pointer;
                    margin-right: 3px;
                    font-size: 12px;
                ">清空</button>
                <button id="closeBtn" style="
                    background: #dc3545;
                    color: white;
                    border: none;
                    padding: 8px 12px;
                    border-radius: 4px;
                    cursor: pointer;
                    font-size: 12px;
                ">关闭</button>
            </div>
            <div style="margin-top: 5px; font-size: 10px; color: #888;">
                当前页面: ${window.location.pathname} | 已保存数据: ${savedData ? '是' : '否'}
            </div>
        `;

        // 添加事件监听器
        const textarea = importPanel.querySelector('#dataInput');
        const importBtn = importPanel.querySelector('#importBtn');
        const autoFillBtn = importPanel.querySelector('#autoFillBtn');
        const autoProcessBtn = importPanel.querySelector('#autoProcessBtn');
        const loadSampleBtn = importPanel.querySelector('#loadSampleBtn');
        const clearBtn = importPanel.querySelector('#clearBtn');
        const closeBtn = importPanel.querySelector('#closeBtn');

        // 保存并导入按钮事件
        importBtn.addEventListener('click', function() {
            const data = textarea.value.trim();
            if (data) {
                GM_setValue(DATA_KEY, data);
                importData(data);
            } else {
                alert('请先粘贴资料！');
            }
        });

        // 自动填写当前页按钮事件
        autoFillBtn.addEventListener('click', function() {
            autoFillCurrentPage();
        });

        // 自动流程按钮事件
        autoProcessBtn.addEventListener('click', function() {
            startAutoProcess();
        });

        // 加载示例数据按钮事件
        loadSampleBtn.addEventListener('click', function() {
            textarea.value = SAMPLE_DATA;
            GM_setValue(DATA_KEY, SAMPLE_DATA);
            alert('示例数据已加载！');
        });

        // 清空按钮事件
        clearBtn.addEventListener('click', function() {
            clearAllFields();
        });

        // 关闭按钮事件
        closeBtn.addEventListener('click', function() {
            importPanel.remove();
        });

        // 将面板添加到页面
        document.body.appendChild(importPanel);

        console.log('TaxSlayer自动填表工具已加载');
    }

    // 导入数据函数
    function importData(data) {
        const trimmedData = data.trim();
        if (!trimmedData) {
            alert('请先粘贴资料！');
            return;
        }

        // 按Tab分割数据
        const fields = trimmedData.split('\t');

        if (fields.length < 27) {
            alert(`数据格式不正确！需要27个字段，当前只有${fields.length}个字段`);
            return;
        }

        try {
            autoFillCurrentPage();
            alert(`资料已保存并导入！\n共${fields.length}个字段\n邮箱: ${fields[0] || '无'}\n用户名: ${fields[1] || '无'}\n当前页面已自动填写`);

        } catch (error) {
            console.error('导入数据时出错:', error);
            alert('导入数据时出错，请检查控制台');
        }
    }

    // 自动填写当前页面
    function autoFillCurrentPage() {
        const savedData = GM_getValue(DATA_KEY, '');
        if (!savedData) {
            alert('没有保存的数据，请先导入资料！');
            return;
        }

        const fields = savedData.split('\t');
        const currentUrl = window.location.href;
        let filledCount = 0;

        try {
            // 根据页面URL判断填写哪些字段
            if (currentUrl.includes('/Register')) {
                // 注册页面
                filledCount += fillRegistrationFields(fields);
            } else if (currentUrl.includes('/personal') || currentUrl.includes('/info')) {
                // 个人信息页面
                filledCount += fillPersonalInfoFields(fields);
            } else if (currentUrl.includes('/tax') || currentUrl.includes('/income')) {
                // 税务信息页面
                filledCount += fillTaxFields(fields);
            } else if (currentUrl.includes('/bank') || currentUrl.includes('/refund')) {
                // 银行信息页面
                filledCount += fillBankFields(fields);
            } else {
                // 通用填写
                filledCount += fillCommonFields(fields);
            }

            if (filledCount > 0) {
                console.log(`已填写 ${filledCount} 个字段`);
            } else {
                console.log('当前页面没有识别到可填写的字段');
            }

        } catch (error) {
            console.error('自动填写时出错:', error);
        }
    }

    // 开始自动流程
    function startAutoProcess() {
        const savedData = GM_getValue(DATA_KEY, '');
        if (!savedData) {
            alert('没有保存的数据，请先导入资料！');
            return;
        }

        if (confirm('开始自动流程？\n将自动填写当前页面并尝试点击下一步按钮。')) {
            executeAutoProcess();
        }
    }

    // 执行自动流程
    function executeAutoProcess() {
        GM_log('开始执行自动流程...');

        // 步骤1: 自动填写当前页面
        autoFillCurrentPage();

        // 步骤2: 等待一段时间后尝试点击下一步
        setTimeout(() => {
            clickNextButton();
        }, 2000);
    }

    // 点击下一步按钮
    function clickNextButton() {
        // 首先检查是否有错误信息
        const errorElements = document.querySelectorAll('.error, .alert, [class*="error"], [class*="alert"]');
        if (errorElements.length > 0) {
            const errorText = Array.from(errorElements).map(el => el.textContent.trim()).join('; ');
            GM_log('发现错误信息:', errorText);

            // 如果是验证码错误，重新填写表单
            if (errorText.toLowerCase().includes('recaptcha') || errorText.toLowerCase().includes('captcha')) {
                alert('检测到验证码错误，将重新填写表单。请手动完成验证码验证。');
                setTimeout(() => {
                    autoFillCurrentPage();
                }, 2000);
                return;
            }
        }

        // 查找各种可能的下一步按钮
        const nextButtonSelectors = [
            'button[type="submit"]:not([disabled])',
            'input[type="submit"]:not([disabled])',
            'button:not([disabled])',
            '.btn-primary:not([disabled])',
            '.btn-submit:not([disabled])'
        ];

        let buttonFound = false;

        for (const selector of nextButtonSelectors) {
            const buttons = document.querySelectorAll(selector);

            for (const button of buttons) {
                const text = (button.textContent || button.value || '').toLowerCase();

                // 检查按钮文本是否包含提交相关词汇
                if (text.includes('create') || text.includes('submit') || text.includes('next') ||
                    text.includes('continue') || text.includes('register') || text.includes('sign up')) {

                    if (isButtonClickable(button)) {
                        GM_log('找到可点击按钮:', button.textContent || button.value);

                        setTimeout(() => {
                            button.click();
                            GM_log('已点击按钮');
                            buttonFound = true;

                            // 点击后等待页面变化
                            setTimeout(() => {
                                checkPageChange();
                            }, 3000);
                        }, 1000);

                        return; // 找到并点击后立即返回
                    }
                }
            }
        }

        if (!buttonFound) {
            GM_log('未找到可点击的下一步按钮');
            alert('未找到可点击的下一步按钮，请手动点击继续。\n可能需要先完成验证码验证。');
        }
    }

    // 检查按钮是否可点击
    function isButtonClickable(button) {
        const style = window.getComputedStyle(button);
        return !button.disabled &&
               style.display !== 'none' &&
               style.visibility !== 'hidden' &&
               button.offsetParent !== null;
    }

    // 检查页面变化
    function checkPageChange() {
        const currentUrl = window.location.href;
        GM_log('当前页面:', currentUrl);

        // 如果页面发生变化，继续自动流程
        setTimeout(() => {
            if (window.location.href !== currentUrl) {
                GM_log('页面已变化，继续自动流程');
                executeAutoProcess();
            } else {
                GM_log('页面未变化，流程结束');
            }
        }, 2000);
    }

    // 注册页面填写
    function fillRegistrationFields(fields) {
        let count = 0;

        // 邮箱
        const emailField = findFieldBySelectors([
            'input[type="email"]',
            'input[placeholder*="email" i]',
            'input[name*="email" i]'
        ]);
        if (emailField && fields[DATA_FIELDS.email]) {
            fillField(emailField, fields[DATA_FIELDS.email]);
            count++;
        }

        // 用户名
        const usernameField = findFieldBySelectors([
            'input[name*="username" i]',
            'input[placeholder*="username" i]'
        ]);
        if (usernameField && fields[DATA_FIELDS.username]) {
            fillField(usernameField, fields[DATA_FIELDS.username]);
            count++;
        }

        // 密码 - 填写所有密码字段
        const passwordFields = document.querySelectorAll('input[type="password"]');
        passwordFields.forEach(passwordField => {
            if (passwordField && fields[DATA_FIELDS.password]) {
                fillField(passwordField, fields[DATA_FIELDS.password]);
                count++;
            }
        });

        // 手机号
        const phoneField = findFieldBySelectors([
            'input[type="tel"]',
            'input[name*="phone" i]',
            'input[placeholder*="phone" i]'
        ]);
        if (phoneField && fields[DATA_FIELDS.phone]) {
            fillField(phoneField, fields[DATA_FIELDS.phone]);
            count++;
        }

        return count;
    }

    // 个人信息页面填写
    function fillPersonalInfoFields(fields) {
        let count = 0;

        // 名字
        const firstNameField = findFieldBySelectors([
            'input[name*="first" i]',
            'input[placeholder*="first" i]',
            'input[name*="fname" i]'
        ]);
        if (firstNameField && fields[DATA_FIELDS.firstName]) {
            fillField(firstNameField, fields[DATA_FIELDS.firstName]);
            count++;
        }

        // 姓氏
        const lastNameField = findFieldBySelectors([
            'input[name*="last" i]',
            'input[placeholder*="last" i]',
            'input[name*="lname" i]'
        ]);
        if (lastNameField && fields[DATA_FIELDS.lastName]) {
            fillField(lastNameField, fields[DATA_FIELDS.lastName]);
            count++;
        }

        // SSN
        const ssnField = findFieldBySelectors([
            'input[name*="ssn" i]',
            'input[placeholder*="ssn" i]',
            'input[name*="social" i]'
        ]);
        if (ssnField && fields[DATA_FIELDS.ssn]) {
            fillField(ssnField, fields[DATA_FIELDS.ssn]);
            count++;
        }

        // 生日
        const birthField = findFieldBySelectors([
            'input[name*="birth" i]',
            'input[placeholder*="birth" i]',
            'input[type="date"]'
        ]);
        if (birthField && fields[DATA_FIELDS.birthDate]) {
            // 转换日期格式 04/09/1968 -> 1968-04-09
            const dateStr = fields[DATA_FIELDS.birthDate];
            if (dateStr.includes('/')) {
                const parts = dateStr.split('/');
                const formattedDate = `${parts[2]}-${parts[0].padStart(2, '0')}-${parts[1].padStart(2, '0')}`;
                fillField(birthField, formattedDate);
            } else {
                fillField(birthField, dateStr);
            }
            count++;
        }

        // 地址
        const addressField = findFieldBySelectors([
            'input[name*="address" i]',
            'input[placeholder*="address" i]',
            'input[name*="street" i]'
        ]);
        if (addressField && fields[DATA_FIELDS.address]) {
            fillField(addressField, fields[DATA_FIELDS.address]);
            count++;
        }

        // 邮编
        const zipField = findFieldBySelectors([
            'input[name*="zip" i]',
            'input[placeholder*="zip" i]',
            'input[name*="postal" i]'
        ]);
        if (zipField && fields[DATA_FIELDS.zipCode]) {
            fillField(zipField, fields[DATA_FIELDS.zipCode]);
            count++;
        }

        return count;
    }

    // 税务信息页面填写
    function fillTaxFields(fields) {
        let count = 0;

        // 雇主
        const employerField = findFieldBySelectors([
            'input[name*="employer" i]',
            'input[placeholder*="employer" i]',
            'input[name*="company" i]'
        ]);
        if (employerField && fields[DATA_FIELDS.employer]) {
            fillField(employerField, fields[DATA_FIELDS.employer]);
            count++;
        }

        // 雇主地址
        const employerAddressField = findFieldBySelectors([
            'input[name*="employer" i][name*="address" i]',
            'input[placeholder*="employer" i][placeholder*="address" i]'
        ]);
        if (employerAddressField && fields[DATA_FIELDS.employerAddress]) {
            fillField(employerAddressField, fields[DATA_FIELDS.employerAddress]);
            count++;
        }

        // 收入相关字段
        const incomeFields = [
            'input[name*="income" i]',
            'input[name*="wage" i]',
            'input[name*="salary" i]',
            'input[name*="amount" i]'
        ];

        incomeFields.forEach((selector, index) => {
            const field = document.querySelector(selector);
            const incomeIndex = DATA_FIELDS.income1 + index;
            if (field && fields[incomeIndex]) {
                fillField(field, fields[incomeIndex]);
                count++;
            }
        });

        return count;
    }

    // 银行信息页面填写
    function fillBankFields(fields) {
        let count = 0;

        // 银行名称
        const bankNameField = findFieldBySelectors([
            'input[name*="bank" i]',
            'input[placeholder*="bank" i]',
            'select[name*="bank" i]'
        ]);
        if (bankNameField && fields[DATA_FIELDS.bankName]) {
            fillField(bankNameField, fields[DATA_FIELDS.bankName]);
            count++;
        }

        // 路由号
        const routingField = findFieldBySelectors([
            'input[name*="routing" i]',
            'input[placeholder*="routing" i]',
            'input[name*="aba" i]'
        ]);
        if (routingField && fields[DATA_FIELDS.routingNumber]) {
            fillField(routingField, fields[DATA_FIELDS.routingNumber]);
            count++;
        }

        // 账号
        const accountField = findFieldBySelectors([
            'input[name*="account" i]',
            'input[placeholder*="account" i]'
        ]);
        if (accountField && fields[DATA_FIELDS.accountNumber]) {
            fillField(accountField, fields[DATA_FIELDS.accountNumber]);
            count++;
        }

        return count;
    }

    // 通用字段填写
    function fillCommonFields(fields) {
        let count = 0;

        // 尝试填写所有可能的字段
        count += fillRegistrationFields(fields);
        count += fillPersonalInfoFields(fields);
        count += fillTaxFields(fields);
        count += fillBankFields(fields);

        return count;
    }

    // 通用字段查找函数
    function findFieldBySelectors(selectors) {
        for (const selector of selectors) {
            const field = document.querySelector(selector);
            if (field) return field;
        }
        return null;
    }

    // 填写字段并触发事件
    function fillField(field, value) {
        if (!field || !value) return;

        // 清空字段
        field.value = '';
        field.focus();

        // 逐字符输入（模拟真实输入）
        for (let i = 0; i < value.length; i++) {
            setTimeout(() => {
                field.value = value.substring(0, i + 1);
                field.dispatchEvent(new Event('input', { bubbles: true }));
                field.dispatchEvent(new Event('keyup', { bubbles: true }));
            }, i * 50);
        }

        // 最后触发change和blur事件
        setTimeout(() => {
            field.dispatchEvent(new Event('change', { bubbles: true }));
            field.dispatchEvent(new Event('blur', { bubbles: true }));

            // 检查字段验证
            checkFieldValidation(field);
        }, value.length * 50 + 100);
    }

    // 检查字段验证
    function checkFieldValidation(field) {
        // 检查是否有验证错误
        const errorElements = document.querySelectorAll('.error, .invalid, .field-error, [class*="error"]');
        if (errorElements.length > 0) {
            GM_log('发现验证错误:', errorElements);
        }

        // 检查字段是否有效
        if (field.checkValidity && !field.checkValidity()) {
            GM_log('字段验证失败:', field.name || field.id, field.validationMessage);
        }
    }

    // 等待元素出现
    function waitForElement(selector, timeout = 10000) {
        return new Promise((resolve, reject) => {
            const element = document.querySelector(selector);
            if (element) {
                resolve(element);
                return;
            }

            const observer = new MutationObserver((mutations, obs) => {
                const element = document.querySelector(selector);
                if (element) {
                    obs.disconnect();
                    resolve(element);
                }
            });

            observer.observe(document.body, {
                childList: true,
                subtree: true
            });

            setTimeout(() => {
                observer.disconnect();
                reject(new Error(`元素 ${selector} 在 ${timeout}ms 内未找到`));
            }, timeout);
        });
    }

    // 等待页面加载完成
    function waitForPageReady() {
        return new Promise((resolve) => {
            if (document.readyState === 'complete') {
                resolve();
            } else {
                window.addEventListener('load', resolve);
            }
        });
    }

    // 清空所有表单字段
    function clearAllFields() {
        const allInputs = document.querySelectorAll('input[type="text"], input[type="email"], input[type="tel"], input[type="password"], input[type="date"], input[type="number"], textarea, select');

        allInputs.forEach(field => {
            fillField(field, '');
        });

        alert('所有字段已清空');
    }

    // 启动脚本
    waitForPageLoad();

})();
