# W-2 表单自动选择器 - 多浏览器使用指南

## 📋 **概述**
这个脚本可以自动在TaxSlayer网站上选择W-2表单并点击Continue按钮，支持多种浏览器和使用方式。

---

## 🎯 **方法一：用户脚本（推荐）**

### **适用浏览器：** Chrome, Firefox, Edge, Safari, Opera
### **需要安装：** Tampermonkey 或 Greasemonkey 扩展

#### **安装步骤：**

1. **安装用户脚本管理器**
   - **Chrome/Edge**: 安装 [Tampermonkey](https://chrome.google.com/webstore/detail/tampermonkey/dhdgffkkebhmkfjojejmpbldmpobfkfo)
   - **Firefox**: 安装 [Tampermonkey](https://addons.mozilla.org/en-US/firefox/addon/tampermonkey/) 或 [Greasemonkey](https://addons.mozilla.org/en-US/firefox/addon/greasemonkey/)
   - **Safari**: 安装 [Tampermonkey](https://apps.apple.com/us/app/tampermonkey/id1482490089)

2. **安装脚本**
   - 打开 `w2-selector-userscript.js` 文件
   - 复制全部内容
   - 在Tampermonkey中点击"创建新脚本"
   - 粘贴代码并保存

3. **使用方法**
   - 访问TaxSlayer的Quick File页面
   - 脚本会自动检测页面并显示控制面板
   - 点击"开始选择 W-2"按钮即可

#### **优点：**
- ✅ 自动检测页面
- ✅ 可视化操作界面
- ✅ 支持页面跳转
- ✅ 一次安装，永久使用

---

## 🔖 **方法二：浏览器书签（简单快捷）**

### **适用浏览器：** 所有现代浏览器
### **无需安装扩展**

#### **安装步骤：**

1. **创建书签**
   - 在浏览器中创建新书签
   - 书签名称：`W-2自动选择器`
   - 书签地址：复制 `w2-selector-bookmarklet.js` 文件中的完整代码

2. **使用方法**
   - 访问TaxSlayer的Quick File页面
   - 点击书签即可运行脚本

#### **优点：**
- ✅ 无需安装扩展
- ✅ 支持所有浏览器
- ✅ 一键执行

#### **缺点：**
- ❌ 需要手动点击书签
- ❌ 页面跳转后需重新点击

---

## 🖥️ **方法三：控制台执行（万能方法）**

### **适用浏览器：** 所有浏览器
### **适用场景：** 临时使用、测试、其他方法失效时

#### **使用步骤：**

1. **打开开发者工具**
   - 按 `F12` 键
   - 或右键点击页面 → "检查元素" → "控制台"

2. **执行脚本**
   - 复制 `w2-selector-console.js` 文件中的全部代码
   - 粘贴到控制台中
   - 按 `Enter` 执行

3. **查看结果**
   - 控制台会显示详细的执行日志
   - 脚本会自动完成W-2选择流程

#### **优点：**
- ✅ 适用于所有浏览器
- ✅ 无需安装任何扩展
- ✅ 详细的调试信息
- ✅ 最高兼容性

#### **缺点：**
- ❌ 每次都需要手动复制粘贴
- ❌ 需要打开开发者工具

---

## 🔧 **故障排除**

### **常见问题：**

1. **脚本没有反应**
   - 确保在正确的页面（Quick File页面）
   - 检查浏览器控制台是否有错误信息
   - 尝试刷新页面后重新运行

2. **找不到W-2选项**
   - 手动点击搜索框激活下拉列表
   - 检查页面是否完全加载
   - 尝试等待几秒后重新运行脚本

3. **点击无效**
   - 确保页面有焦点（点击页面任意位置）
   - 检查是否有弹窗或遮罩层
   - 尝试关闭广告拦截器

4. **用户脚本不工作**
   - 检查Tampermonkey是否启用
   - 确认脚本的@match规则包含当前网站
   - 尝试重新安装脚本

### **调试技巧：**

1. **查看控制台日志**
   - 按F12打开开发者工具
   - 查看Console标签页的输出信息

2. **检查页面元素**
   - 使用开发者工具检查页面结构
   - 确认搜索框和选项的选择器是否正确

3. **手动测试**
   - 先手动执行一遍流程
   - 观察页面变化和元素状态

---

## 🌟 **推荐使用方式**

### **日常使用：** 用户脚本（方法一）
- 自动化程度最高
- 用户体验最好
- 适合经常使用的用户

### **偶尔使用：** 浏览器书签（方法二）
- 简单快捷
- 无需安装扩展
- 适合偶尔使用的用户

### **应急使用：** 控制台执行（方法三）
- 兼容性最好
- 调试信息最详细
- 适合故障排除和测试

---

## 📝 **注意事项**

1. **网站更新**
   - 如果TaxSlayer更新了页面结构，脚本可能需要更新
   - 遇到问题时请检查是否有新版本

2. **浏览器兼容性**
   - 现代浏览器（Chrome 60+, Firefox 55+, Edge 79+, Safari 12+）
   - 旧版本浏览器可能需要使用控制台方法

3. **安全性**
   - 脚本只在TaxSlayer网站运行
   - 不会收集或传输任何个人信息
   - 建议从可信来源获取脚本

4. **使用限制**
   - 仅适用于TaxSlayer网站的Quick File页面
   - 需要页面完全加载后才能使用
   - 某些网络环境可能影响脚本执行

---

## 🆘 **获取帮助**

如果遇到问题：
1. 首先查看故障排除部分
2. 检查浏览器控制台的错误信息
3. 尝试不同的使用方法
4. 确保网站页面结构没有变化
