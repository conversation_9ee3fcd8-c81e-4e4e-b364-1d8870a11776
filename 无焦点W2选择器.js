// W-2选择器 - 无焦点限制版本
// 使用多种技术绕过浏览器焦点限制

javascript:(function(){
    console.log('🚀 无焦点W-2选择器启动');
    
    // 方法1: 使用MutationObserver监听DOM变化
    function createDOMObserver() {
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList') {
                    // 检查是否有新的选项出现
                    const options = document.querySelectorAll('[role="option"]');
                    if (options.length > 0) {
                        selectW2Option(options);
                    }
                }
            });
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
        
        return observer;
    }
    
    // 方法2: 直接操作DOM属性，绕过事件系统
    function directDOMManipulation(element, value) {
        // 直接设置value属性
        Object.defineProperty(element, 'value', {
            value: value,
            writable: true
        });
        
        // 触发React的内部更新
        const nativeInputValueSetter = Object.getOwnPropertyDescriptor(
            window.HTMLInputElement.prototype, 'value'
        ).set;
        nativeInputValueSetter.call(element, value);
        
        // 触发React事件
        const event = new Event('input', { bubbles: true });
        element.dispatchEvent(event);
    }
    
    // 方法3: 使用requestAnimationFrame绕过时序限制
    function scheduleAction(action, delay = 0) {
        return new Promise((resolve) => {
            setTimeout(() => {
                requestAnimationFrame(() => {
                    requestAnimationFrame(() => {
                        action();
                        resolve();
                    });
                });
            }, delay);
        });
    }
    
    // 方法4: 模拟真实用户交互
    function simulateRealUserInteraction(element) {
        const rect = element.getBoundingClientRect();
        const x = rect.left + rect.width / 2;
        const y = rect.top + rect.height / 2;
        
        // 创建更真实的鼠标事件
        const events = [
            new MouseEvent('mouseover', {
                view: window,
                bubbles: true,
                cancelable: true,
                clientX: x,
                clientY: y
            }),
            new MouseEvent('mousedown', {
                view: window,
                bubbles: true,
                cancelable: true,
                clientX: x,
                clientY: y,
                button: 0
            }),
            new MouseEvent('mouseup', {
                view: window,
                bubbles: true,
                cancelable: true,
                clientX: x,
                clientY: y,
                button: 0
            }),
            new MouseEvent('click', {
                view: window,
                bubbles: true,
                cancelable: true,
                clientX: x,
                clientY: y,
                button: 0
            })
        ];
        
        events.forEach((event, index) => {
            setTimeout(() => {
                element.dispatchEvent(event);
            }, index * 10);
        });
    }
    
    // 方法5: 使用iframe技术
    function createIframeWorker() {
        const iframe = document.createElement('iframe');
        iframe.style.display = 'none';
        document.body.appendChild(iframe);
        
        // 在iframe中执行操作，有时可以绕过限制
        return iframe.contentWindow;
    }
    
    // 方法6: 使用Web Workers (如果支持)
    function createWorkerHelper() {
        try {
            const workerCode = `
                self.onmessage = function(e) {
                    // 在worker中处理一些逻辑
                    self.postMessage({type: 'ready'});
                };
            `;
            const blob = new Blob([workerCode], {type: 'application/javascript'});
            const worker = new Worker(URL.createObjectURL(blob));
            return worker;
        } catch (e) {
            console.log('Worker不支持，使用备用方案');
            return null;
        }
    }
    
    // 核心W-2选择函数 - 无焦点版本
    async function selectW2WithoutFocus() {
        console.log('🔍 开始无焦点W-2选择...');
        
        // 检查页面
        const pageTitle = document.querySelector('h1');
        if (!pageTitle || pageTitle.textContent !== 'Quick File') {
            console.log('❌ 不在Quick File页面');
            return false;
        }
        
        // 方法1: 直接查找已存在的选项
        let options = document.querySelectorAll('[role="option"]');
        if (options.length > 0) {
            console.log('✅ 找到现有选项，直接选择');
            return selectW2Option(options);
        }
        
        // 方法2: 强制触发下拉列表
        const searchBox = document.querySelector('#autocomplete-input') || 
                         document.querySelector('input[type="text"]');
        
        if (searchBox) {
            console.log('✅ 找到搜索框，尝试多种激活方法');
            
            // 尝试1: 直接设置属性
            directDOMManipulation(searchBox, '');
            
            // 尝试2: 模拟真实交互
            await scheduleAction(() => {
                simulateRealUserInteraction(searchBox);
            }, 100);
            
            // 尝试3: 强制触发事件
            await scheduleAction(() => {
                const events = ['focus', 'click', 'input', 'keydown', 'keyup'];
                events.forEach(eventType => {
                    const event = new Event(eventType, { bubbles: true });
                    searchBox.dispatchEvent(event);
                });
            }, 200);
            
            // 等待选项出现
            for (let i = 0; i < 20; i++) {
                await new Promise(resolve => setTimeout(resolve, 100));
                options = document.querySelectorAll('[role="option"]');
                if (options.length > 0) {
                    console.log(`✅ 选项出现，尝试第${i+1}次`);
                    return selectW2Option(options);
                }
            }
        }
        
        // 方法3: 查找隐藏的选项列表
        const hiddenOptions = document.querySelectorAll('li, div, span');
        for (let element of hiddenOptions) {
            if (element.textContent && element.textContent.includes('W-2')) {
                console.log('✅ 找到隐藏的W-2选项');
                simulateRealUserInteraction(element);
                await new Promise(resolve => setTimeout(resolve, 1000));
                return checkContinueButton();
            }
        }
        
        console.log('❌ 所有方法都失败了');
        return false;
    }
    
    // 选择W-2选项
    function selectW2Option(options) {
        for (let option of options) {
            if (option.textContent && option.textContent.includes('W-2')) {
                console.log('✅ 找到W-2选项，点击选择');
                simulateRealUserInteraction(option);
                
                // 等待选择完成后检查Continue按钮
                setTimeout(() => {
                    checkContinueButton();
                }, 1500);
                
                return true;
            }
        }
        return false;
    }
    
    // 检查并点击Continue按钮
    function checkContinueButton() {
        const continueBtn = Array.from(document.querySelectorAll('button')).find(btn => 
            btn.textContent && btn.textContent.includes('Continue')
        );
        
        if (continueBtn) {
            console.log('✅ 找到Continue按钮，点击');
            simulateRealUserInteraction(continueBtn);
            console.log('🎉 W-2选择流程完成！');
            return true;
        } else {
            console.log('❌ 未找到Continue按钮');
            return false;
        }
    }
    
    // 启动DOM观察器
    const observer = createDOMObserver();
    
    // 立即尝试选择
    selectW2WithoutFocus().then(success => {
        if (success) {
            console.log('🎉 无焦点W-2选择成功！');
            observer.disconnect();
        } else {
            console.log('⏳ 继续监听DOM变化...');
            // 保持观察器运行，等待页面变化
        }
    });
    
    // 5秒后如果还没成功，尝试备用方案
    setTimeout(() => {
        console.log('🔄 尝试备用方案...');
        
        // 备用方案：查找所有可能的W-2相关元素
        const allElements = document.querySelectorAll('*');
        for (let element of allElements) {
            if (element.textContent && 
                element.textContent.includes('W-2') && 
                (element.tagName === 'BUTTON' || 
                 element.tagName === 'A' || 
                 element.tagName === 'DIV' ||
                 element.getAttribute('role') === 'option')) {
                
                console.log('🎯 备用方案：找到W-2相关元素');
                simulateRealUserInteraction(element);
                break;
            }
        }
    }, 5000);
    
    return '🚀 无焦点W-2选择器已启动，正在尝试多种方法...';
})();
