// W-2 选择器 - 完全自动执行版本
// 将下面的代码复制并创建为浏览器书签，点击即可完全自动执行

javascript:(function(){
    console.log('🚀 W-2自动选择器启动');
    
    // 创建状态显示面板
    function createStatusPanel() {
        if(document.getElementById('w2-auto-selector')){
            document.getElementById('w2-auto-selector').remove();
        }
        
        const panel = document.createElement('div');
        panel.id = 'w2-auto-selector';
        panel.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #4CAF50;
            color: white;
            padding: 15px;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.3);
            z-index: 999999;
            font-family: Arial, sans-serif;
            font-size: 14px;
            min-width: 280px;
            max-width: 350px;
        `;
        
        panel.innerHTML = `
            <div style="margin-bottom: 10px; font-weight: bold;">
                ⚡ W-2 自动选择器
            </div>
            <div id="w2-status" style="margin-bottom: 10px; font-size: 12px; line-height: 1.4;">
                正在启动...
            </div>
            <button id="w2-close-btn" style="
                background: rgba(255,255,255,0.2);
                color: white;
                border: none;
                padding: 6px 12px;
                border-radius: 4px;
                cursor: pointer;
                font-size: 12px;
            ">关闭</button>
        `;
        
        document.body.appendChild(panel);
        
        document.getElementById('w2-close-btn').addEventListener('click', () => {
            panel.remove();
        });
        
        return panel;
    }
    
    // 更新状态显示
    function updateStatus(message, isError = false, isSuccess = false) {
        const statusDiv = document.getElementById('w2-status');
        const panel = document.getElementById('w2-auto-selector');
        
        if (statusDiv) {
            statusDiv.textContent = message;
        }
        
        if (panel) {
            if (isError) {
                panel.style.background = '#f44336';
            } else if (isSuccess) {
                panel.style.background = '#4CAF50';
            } else {
                panel.style.background = '#2196F3';
            }
        }
        
        console.log(isError ? '❌' : isSuccess ? '🎉' : '✅', message);
    }
    
    // 模拟点击函数
    function simulateClick(element) {
        const events = ['mousedown', 'mouseup', 'click'];
        events.forEach(eventType => {
            const event = new MouseEvent(eventType, {
                view: window,
                bubbles: true,
                cancelable: true
            });
            element.dispatchEvent(event);
        });
    }
    
    // 检查W-2是否已选择
    function isW2Selected() {
        const selectedFormsList = document.querySelector('[role="list"]');
        if (selectedFormsList) {
            const w2Item = Array.from(selectedFormsList.querySelectorAll('*')).find(el => 
                el.textContent && el.textContent.includes('W-2')
            );
            if (w2Item) return true;
        }
        
        const noFormsText = Array.from(document.querySelectorAll('*')).find(el => 
            el.textContent && el.textContent.includes('No forms currently selected')
        );
        return !noFormsText;
    }
    
    // 主要执行函数
    async function executeW2Selection() {
        try {
            updateStatus('检查当前页面...');
            
            const pageTitle = document.querySelector('h1');
            if (!pageTitle || pageTitle.textContent !== 'Quick File') {
                updateStatus('错误：不在 Quick File 页面', true);
                return false;
            }
            
            // 检查是否已经选择了W-2
            if (isW2Selected()) {
                updateStatus('W-2 已选择，查找 Continue 按钮...');
                const continueBtn = Array.from(document.querySelectorAll('button')).find(btn => 
                    btn.textContent && btn.textContent.includes('Continue')
                );
                
                if (continueBtn) {
                    updateStatus('点击 Continue 按钮...');
                    simulateClick(continueBtn);
                    updateStatus('流程完成！页面即将跳转...', false, true);
                    
                    // 3秒后自动关闭面板
                    setTimeout(() => {
                        const panel = document.getElementById('w2-auto-selector');
                        if (panel) panel.remove();
                    }, 3000);
                    
                    return true;
                } else {
                    updateStatus('错误：未找到 Continue 按钮', true);
                    return false;
                }
            }
            
            updateStatus('查找搜索框...');
            const searchBox = document.querySelector('#autocomplete-input') || 
                             document.querySelector('input[type="text"]');
            
            if (!searchBox) {
                updateStatus('错误：未找到搜索框', true);
                return false;
            }
            
            updateStatus('激活搜索框...');
            searchBox.value = '';
            searchBox.focus();
            simulateClick(searchBox);
            
            updateStatus('等待选项列表出现...');
            await new Promise(resolve => setTimeout(resolve, 1500));
            
            const allOptions = document.querySelectorAll('[role="option"]');
            updateStatus(`找到 ${allOptions.length} 个选项，查找 W-2...`);
            
            let w2Option = null;
            for (let option of allOptions) {
                if (option.textContent && option.textContent.includes('W-2')) {
                    w2Option = option;
                    break;
                }
            }
            
            if (!w2Option) {
                updateStatus('错误：未找到 W-2 选项', true);
                return false;
            }
            
            updateStatus('找到 W-2 选项，正在选择...');
            simulateClick(w2Option);
            
            updateStatus('等待选择完成...');
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            // 验证选择结果
            if (isW2Selected()) {
                updateStatus('W-2 选择成功！查找 Continue 按钮...');
                const continueBtn = Array.from(document.querySelectorAll('button')).find(btn => 
                    btn.textContent && btn.textContent.includes('Continue')
                );
                
                if (continueBtn) {
                    updateStatus('点击 Continue 按钮...');
                    simulateClick(continueBtn);
                    updateStatus('🎉 W-2 选择流程完成！页面即将跳转...', false, true);
                    
                    // 5秒后自动关闭面板
                    setTimeout(() => {
                        const panel = document.getElementById('w2-auto-selector');
                        if (panel) panel.remove();
                    }, 5000);
                    
                    return true;
                } else {
                    updateStatus('错误：选择成功但未找到 Continue 按钮', true);
                    return false;
                }
            } else {
                updateStatus('错误：W-2 选择失败', true);
                return false;
            }
            
        } catch (error) {
            updateStatus(`执行错误：${error.message}`, true);
            console.error('❌ 执行错误:', error);
            return false;
        }
    }
    
    // 创建状态面板
    createStatusPanel();
    
    // 立即开始自动执行
    updateStatus('开始自动执行 W-2 选择...');
    setTimeout(() => {
        executeW2Selection();
    }, 500);
    
    return '🚀 W-2自动选择器已启动';
})();
