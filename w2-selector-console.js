// 控制台直接执行版本 - 适用于任何浏览器
// 复制下面的代码到浏览器控制台（F12）中执行

(function() {
    'use strict';
    
    console.log('🚀 开始执行W-2表单选择脚本（通用版本）');
    
    // 延迟函数
    function delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    
    // 模拟点击函数
    function simulateClick(element) {
        if (!element) return false;
        
        // 多种点击方式确保兼容性
        const events = ['mousedown', 'mouseup', 'click'];
        events.forEach(eventType => {
            try {
                const event = new MouseEvent(eventType, {
                    view: window,
                    bubbles: true,
                    cancelable: true
                });
                element.dispatchEvent(event);
            } catch (e) {
                // 兼容旧浏览器
                const event = document.createEvent('MouseEvent');
                event.initMouseEvent(eventType, true, true, window, 0, 0, 0, 0, 0, false, false, false, false, 0, null);
                element.dispatchEvent(event);
            }
        });
        
        // 直接调用click方法
        try {
            element.click();
        } catch (e) {
            console.warn('直接click失败:', e);
        }
        
        console.log('🖱️ 已点击元素:', element.textContent || element.tagName);
        return true;
    }
    
    // 检查W-2是否已选择
    function isW2Selected() {
        // 方法1: 检查Selected Forms列表
        const selectedFormsList = document.querySelector('[role="list"]');
        if (selectedFormsList) {
            const w2Item = Array.from(selectedFormsList.querySelectorAll('*')).find(el => 
                el.textContent && el.textContent.includes('W-2')
            );
            if (w2Item) {
                console.log('✅ 在Selected Forms中找到W-2');
                return true;
            }
        }
        
        // 方法2: 检查是否还有"No forms currently selected"文本
        const noFormsText = Array.from(document.querySelectorAll('*')).find(el => 
            el.textContent && el.textContent.includes('No forms currently selected')
        );
        
        const hasW2Selected = !noFormsText;
        console.log('📋 W-2选择状态:', hasW2Selected ? '已选择' : '未选择');
        return hasW2Selected;
    }
    
    // 查找元素的通用函数
    function findElement(selectors, textContent = null) {
        for (const selector of selectors) {
            try {
                const elements = document.querySelectorAll(selector);
                for (const element of elements) {
                    if (!textContent || (element.textContent && element.textContent.includes(textContent))) {
                        return element;
                    }
                }
            } catch (e) {
                console.warn(`选择器失败: ${selector}`, e);
            }
        }
        return null;
    }
    
    // 主要执行函数
    async function executeW2Selection() {
        try {
            console.log('🔍 检查当前页面...');
            
            // 检查页面标题
            const pageTitle = document.querySelector('h1');
            if (!pageTitle || pageTitle.textContent !== 'Quick File') {
                console.log('❌ 不在Quick File页面，当前页面标题:', pageTitle ? pageTitle.textContent : '未找到');
                return false;
            }
            
            console.log('✅ 确认在Quick File页面');
            
            // 检查是否已经选择了W-2
            if (isW2Selected()) {
                console.log('✅ W-2表单已选择，查找Continue按钮');
                
                const continueBtn = findElement([
                    'button',
                    '[role="button"]',
                    'input[type="submit"]',
                    'input[type="button"]'
                ], 'Continue');
                
                if (continueBtn) {
                    console.log('✅ 找到Continue按钮，点击提交');
                    simulateClick(continueBtn);
                    console.log('🎉 流程完成！');
                    return true;
                } else {
                    console.log('❌ 未找到Continue按钮');
                    return false;
                }
            }
            
            console.log('🔍 开始选择W-2表单...');
            
            // 查找搜索框
            const searchBox = findElement([
                '#autocomplete-input',
                'textbox[name="Form search"]',
                'input[type="text"]',
                'input[placeholder*="search"]',
                'input[placeholder*="Search"]'
            ]);
            
            if (!searchBox) {
                console.log('❌ 未找到搜索框');
                return false;
            }
            
            console.log('✅ 找到搜索框，激活下拉列表');
            
            // 清空并激活搜索框
            searchBox.value = '';
            searchBox.focus();
            
            // 触发多种事件确保兼容性
            const inputEvents = ['focus', 'click', 'mousedown', 'mouseup', 'input', 'change'];
            inputEvents.forEach(eventType => {
                try {
                    const event = new Event(eventType, { bubbles: true });
                    searchBox.dispatchEvent(event);
                } catch (e) {
                    // 兼容旧浏览器
                    const event = document.createEvent('Event');
                    event.initEvent(eventType, true, true);
                    searchBox.dispatchEvent(event);
                }
            });
            
            simulateClick(searchBox);
            
            // 等待下拉列表出现
            console.log('⏳ 等待下拉列表出现...');
            await delay(1500);
            
            // 查找W-2选项
            const allOptions = document.querySelectorAll('[role="option"]');
            console.log(`📋 找到 ${allOptions.length} 个选项`);
            
            let w2Option = null;
            for (const option of allOptions) {
                if (option.textContent && option.textContent.includes('W-2')) {
                    w2Option = option;
                    console.log('✅ 找到W-2选项:', option.textContent);
                    break;
                }
            }
            
            if (!w2Option) {
                console.log('❌ 未找到W-2选项');
                console.log('可用选项:', Array.from(allOptions).map(opt => opt.textContent));
                
                // 尝试强制显示隐藏的选项
                console.log('🔧 尝试显示隐藏的选项...');
                const allElements = document.querySelectorAll('*');
                for (const el of allElements) {
                    if (el.textContent && el.textContent.includes('W-2') && el.textContent.includes('Wages')) {
                        el.style.display = 'block';
                        el.style.visibility = 'visible';
                        el.style.opacity = '1';
                        if (!el.getAttribute('role')) {
                            el.setAttribute('role', 'option');
                        }
                        console.log('🔧 显示隐藏的W-2选项:', el.textContent);
                    }
                }
                
                // 重新查找
                await delay(500);
                const newOptions = document.querySelectorAll('[role="option"]');
                for (const option of newOptions) {
                    if (option.textContent && option.textContent.includes('W-2')) {
                        w2Option = option;
                        break;
                    }
                }
            }
            
            if (!w2Option) {
                console.log('❌ 仍未找到W-2选项，脚本失败');
                return false;
            }
            
            // 点击W-2选项
            console.log('✅ 点击W-2选项');
            simulateClick(w2Option);
            
            // 等待选择完成
            console.log('⏳ 等待选择完成...');
            await delay(2000);
            
            // 验证W-2是否已选择
            if (isW2Selected()) {
                console.log('✅ W-2表单选择成功！');
                
                // 查找并点击Continue按钮
                const continueBtn = findElement([
                    'button',
                    '[role="button"]',
                    'input[type="submit"]',
                    'input[type="button"]'
                ], 'Continue');
                
                if (continueBtn) {
                    console.log('✅ 找到Continue按钮，点击提交');
                    simulateClick(continueBtn);
                    console.log('🎉 W-2表单选择流程完成！');
                    return true;
                } else {
                    console.log('❌ 未找到Continue按钮');
                    return false;
                }
            } else {
                console.log('❌ W-2表单选择失败');
                return false;
            }
            
        } catch (error) {
            console.error('❌ W-2表单选择过程中出错:', error);
            return false;
        }
    }
    
    // 执行主函数
    executeW2Selection().then(success => {
        if (success) {
            console.log('🎉 脚本执行成功！');
        } else {
            console.log('❌ 脚本执行失败，请检查页面状态或手动操作');
        }
    });
    
    return '通用W-2选择脚本已启动，请查看控制台日志';
    
})();
