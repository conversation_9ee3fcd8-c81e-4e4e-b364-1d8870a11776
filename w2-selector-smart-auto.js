// ==UserScript==
// @name         TaxSlayer W-2 智能自动选择器
// @namespace    http://tampermonkey.net/
// @version      1.2
// @description  智能检测用户交互并自动选择W-2表单，解决浏览器安全限制
// <AUTHOR>
// @match        https://www.taxslayer.com/*
// @match        https://taxslayer.com/*
// @match        https://*.taxslayer.com/*
// @grant        none
// @run-at       document-idle
// ==/UserScript==

(function() {
    'use strict';
    
    console.log('🚀 TaxSlayer W-2 智能自动选择器已加载');
    
    let hasUserInteracted = false;
    let autoExecuteTimer = null;
    
    // 检查是否在Quick File页面
    function isQuickFilePage() {
        const pageTitle = document.querySelector('h1');
        return pageTitle && pageTitle.textContent === 'Quick File';
    }
    
    // 创建智能控制面板
    function createSmartControlPanel() {
        // 避免重复创建
        if (document.getElementById('w2-auto-selector')) {
            return;
        }
        
        const panel = document.createElement('div');
        panel.id = 'w2-auto-selector';
        panel.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #FF9800;
            color: white;
            padding: 15px;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.3);
            z-index: 999999;
            font-family: Arial, sans-serif;
            font-size: 14px;
            min-width: 280px;
            max-width: 350px;
            cursor: pointer;
            transition: all 0.3s ease;
        `;
        
        panel.innerHTML = `
            <div style="margin-bottom: 10px; font-weight: bold;">
                🎯 W-2 智能自动选择器
            </div>
            <div id="w2-instruction" style="margin-bottom: 10px; font-size: 12px; line-height: 1.4;">
                👆 点击此面板激活自动执行
            </div>
            <div id="w2-status" style="margin-bottom: 10px; font-size: 12px; line-height: 1.4;">
                等待用户交互...
            </div>
            <button id="w2-close-btn" style="
                background: rgba(255,255,255,0.2);
                color: white;
                border: none;
                padding: 6px 12px;
                border-radius: 4px;
                cursor: pointer;
                font-size: 12px;
            ">关闭</button>
        `;
        
        document.body.appendChild(panel);
        
        // 绑定关闭事件
        document.getElementById('w2-close-btn').addEventListener('click', (e) => {
            e.stopPropagation();
            panel.remove();
            if (autoExecuteTimer) {
                clearTimeout(autoExecuteTimer);
            }
        });
        
        // 点击面板激活
        panel.addEventListener('click', (e) => {
            if (e.target.id !== 'w2-close-btn') {
                activateAutoExecution();
            }
        });
        
        // 监听全局用户交互
        setupUserInteractionListeners();
        
        // 如果页面已经有焦点，显示不同的提示
        if (document.hasFocus()) {
            updateStatus('页面已有焦点，点击面板开始执行');
            panel.style.background = '#4CAF50';
        }
        
        return panel;
    }
    
    // 设置用户交互监听器
    function setupUserInteractionListeners() {
        const events = ['click', 'keydown', 'mousedown', 'touchstart'];
        
        const interactionHandler = (e) => {
            if (!hasUserInteracted && !e.target.closest('#w2-auto-selector')) {
                hasUserInteracted = true;
                updateStatus('检测到用户交互，准备自动执行...');
                
                // 移除监听器
                events.forEach(event => {
                    document.removeEventListener(event, interactionHandler, true);
                });
                
                // 延迟执行
                autoExecuteTimer = setTimeout(() => {
                    activateAutoExecution();
                }, 1500);
            }
        };
        
        // 添加监听器
        events.forEach(event => {
            document.addEventListener(event, interactionHandler, true);
        });
    }
    
    // 激活自动执行
    function activateAutoExecution() {
        if (hasUserInteracted) return; // 避免重复执行
        
        hasUserInteracted = true;
        const panel = document.getElementById('w2-auto-selector');
        
        if (panel) {
            panel.style.background = '#2196F3';
            updateInstruction('🚀 自动执行中...');
        }
        
        updateStatus('开始自动执行 W-2 选择...');
        executeW2Selection();
    }
    
    // 更新指令显示
    function updateInstruction(message) {
        const instructionDiv = document.getElementById('w2-instruction');
        if (instructionDiv) {
            instructionDiv.textContent = message;
        }
    }
    
    // 更新状态显示
    function updateStatus(message, isError = false, isSuccess = false) {
        const statusDiv = document.getElementById('w2-status');
        const panel = document.getElementById('w2-auto-selector');
        
        if (statusDiv) {
            statusDiv.textContent = message;
        }
        
        if (panel) {
            if (isError) {
                panel.style.background = '#f44336';
            } else if (isSuccess) {
                panel.style.background = '#4CAF50';
            }
        }
        
        console.log(isError ? '❌' : isSuccess ? '🎉' : '✅', message);
    }
    
    // 模拟点击的函数
    function simulateClick(element) {
        const events = ['mousedown', 'mouseup', 'click'];
        events.forEach(eventType => {
            const event = new MouseEvent(eventType, {
                view: window,
                bubbles: true,
                cancelable: true
            });
            element.dispatchEvent(event);
        });
        console.log('🖱️ 已点击元素:', element.textContent || element.tagName);
    }
    
    // 检查W-2是否已选择
    function isW2Selected() {
        const selectedFormsList = document.querySelector('[role="list"]');
        if (selectedFormsList) {
            const w2Item = Array.from(selectedFormsList.querySelectorAll('*')).find(el => 
                el.textContent && el.textContent.includes('W-2')
            );
            if (w2Item) {
                return true;
            }
        }
        
        const noFormsText = Array.from(document.querySelectorAll('*')).find(el => 
            el.textContent && el.textContent.includes('No forms currently selected')
        );
        return !noFormsText;
    }
    
    // 主要的W-2选择函数
    async function executeW2Selection() {
        try {
            updateStatus('检查当前页面...');
            
            // 检查页面
            if (!isQuickFilePage()) {
                updateStatus('错误：不在 Quick File 页面', true);
                return false;
            }
            
            // 检查是否已经选择了W-2
            if (isW2Selected()) {
                updateStatus('W-2 已选择，查找 Continue 按钮...');
                
                const continueBtn = Array.from(document.querySelectorAll('button')).find(btn => 
                    btn.textContent && btn.textContent.includes('Continue')
                );
                
                if (continueBtn) {
                    updateStatus('点击 Continue 按钮...');
                    simulateClick(continueBtn);
                    updateStatus('流程完成！页面即将跳转...', false, true);
                    
                    // 成功后自动关闭面板
                    setTimeout(() => {
                        const panel = document.getElementById('w2-auto-selector');
                        if (panel) panel.remove();
                    }, 3000);
                    
                    return true;
                } else {
                    updateStatus('错误：未找到 Continue 按钮', true);
                    return false;
                }
            }
            
            // 查找搜索框
            updateStatus('查找搜索框...');
            const searchBox = document.querySelector('#autocomplete-input') || 
                             document.querySelector('textbox[name="Form search"]') ||
                             document.querySelector('input[type="text"]');
            
            if (!searchBox) {
                updateStatus('错误：未找到搜索框', true);
                return false;
            }
            
            // 激活搜索框
            updateStatus('激活搜索框...');
            searchBox.value = '';
            searchBox.focus();
            simulateClick(searchBox);
            
            // 等待下拉列表
            updateStatus('等待选项列表出现...');
            await new Promise(resolve => setTimeout(resolve, 1500));
            
            // 查找W-2选项
            const allOptions = document.querySelectorAll('[role="option"]');
            updateStatus(`找到 ${allOptions.length} 个选项，查找 W-2...`);
            
            let w2Option = null;
            for (let option of allOptions) {
                if (option.textContent && option.textContent.includes('W-2')) {
                    w2Option = option;
                    break;
                }
            }
            
            if (!w2Option) {
                updateStatus('错误：未找到 W-2 选项', true);
                return false;
            }
            
            // 点击W-2选项
            updateStatus('选择 W-2 表单...');
            simulateClick(w2Option);
            
            // 等待选择完成
            updateStatus('等待选择完成...');
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            // 验证并点击Continue
            if (isW2Selected()) {
                updateStatus('W-2 选择成功！查找 Continue 按钮...');
                
                const continueBtn = Array.from(document.querySelectorAll('button')).find(btn => 
                    btn.textContent && btn.textContent.includes('Continue')
                );
                
                if (continueBtn) {
                    updateStatus('点击 Continue 按钮...');
                    simulateClick(continueBtn);
                    updateStatus('🎉 W-2 选择流程完成！', false, true);
                    
                    // 成功后自动关闭面板
                    setTimeout(() => {
                        const panel = document.getElementById('w2-auto-selector');
                        if (panel) panel.remove();
                    }, 5000);
                    
                    return true;
                } else {
                    updateStatus('错误：选择成功但未找到 Continue 按钮', true);
                    return false;
                }
            } else {
                updateStatus('错误：W-2 选择失败', true);
                return false;
            }
            
        } catch (error) {
            updateStatus(`执行错误：${error.message}`, true);
            console.error('❌ 执行错误:', error);
            return false;
        }
    }
    
    // 页面加载完成后检查并显示控制面板
    function init() {
        // 等待页面完全加载
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', init);
            return;
        }
        
        // 检查是否在目标页面
        if (isQuickFilePage()) {
            console.log('✅ 检测到 Quick File 页面，显示智能控制面板');
            createSmartControlPanel();
        }
    }
    
    // 监听页面变化（SPA应用）
    let lastUrl = location.href;
    new MutationObserver(() => {
        const url = location.href;
        if (url !== lastUrl) {
            lastUrl = url;
            hasUserInteracted = false; // 重置交互状态
            if (autoExecuteTimer) {
                clearTimeout(autoExecuteTimer);
                autoExecuteTimer = null;
            }
            setTimeout(init, 1000); // 延迟检查新页面
        }
    }).observe(document, { subtree: true, childList: true });
    
    // 初始化
    init();
    
})();
