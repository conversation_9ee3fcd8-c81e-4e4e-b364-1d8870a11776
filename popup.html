<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <style>
        body {
            width: 350px;
            padding: 20px;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .header h1 {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
        }
        
        .header p {
            margin: 5px 0 0 0;
            font-size: 12px;
            opacity: 0.8;
        }
        
        .section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
            backdrop-filter: blur(10px);
        }
        
        .section h3 {
            margin: 0 0 10px 0;
            font-size: 14px;
            font-weight: 600;
        }
        
        .toggle-group {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 10px;
        }
        
        .toggle-group:last-child {
            margin-bottom: 0;
        }
        
        .toggle-label {
            font-size: 13px;
            flex: 1;
        }
        
        .toggle {
            position: relative;
            width: 50px;
            height: 24px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .toggle.active {
            background: #4CAF50;
        }
        
        .toggle-slider {
            position: absolute;
            top: 2px;
            left: 2px;
            width: 20px;
            height: 20px;
            background: white;
            border-radius: 50%;
            transition: all 0.3s ease;
        }
        
        .toggle.active .toggle-slider {
            transform: translateX(26px);
        }
        
        .status {
            text-align: center;
            padding: 10px;
            border-radius: 8px;
            font-size: 12px;
            margin-bottom: 15px;
        }
        
        .status.active {
            background: rgba(76, 175, 80, 0.2);
            border: 1px solid rgba(76, 175, 80, 0.5);
        }
        
        .status.inactive {
            background: rgba(244, 67, 54, 0.2);
            border: 1px solid rgba(244, 67, 54, 0.5);
        }
        
        .buttons {
            display: flex;
            gap: 10px;
        }
        
        .btn {
            flex: 1;
            padding: 10px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 12px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: #4CAF50;
            color: white;
        }
        
        .btn-primary:hover {
            background: #45a049;
        }
        
        .btn-secondary {
            background: rgba(255, 255, 255, 0.2);
            color: white;
        }
        
        .btn-secondary:hover {
            background: rgba(255, 255, 255, 0.3);
        }
        
        .version {
            text-align: center;
            font-size: 10px;
            opacity: 0.6;
            margin-top: 15px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🎯 TaxSlayer 助手</h1>
        <p>智能自动填写工具</p>
    </div>
    
    <div id="status" class="status">
        <span id="statusText">检查中...</span>
    </div>
    
    <div class="section">
        <h3>⚡ 自动化功能</h3>
        <div class="toggle-group">
            <span class="toggle-label">W-2 自动选择</span>
            <div class="toggle active" id="w2Toggle">
                <div class="toggle-slider"></div>
            </div>
        </div>
        <div class="toggle-group">
            <span class="toggle-label">地址自动填写</span>
            <div class="toggle active" id="addressToggle">
                <div class="toggle-slider"></div>
            </div>
        </div>
        <div class="toggle-group">
            <span class="toggle-label">纳税人信息填写</span>
            <div class="toggle active" id="taxpayerToggle">
                <div class="toggle-slider"></div>
            </div>
        </div>
    </div>
    
    <div class="section">
        <h3>🔧 系统设置</h3>
        <div class="toggle-group">
            <span class="toggle-label">自动模式</span>
            <div class="toggle active" id="autoToggle">
                <div class="toggle-slider"></div>
            </div>
        </div>
        <div class="toggle-group">
            <span class="toggle-label">调试模式</span>
            <div class="toggle active" id="debugToggle">
                <div class="toggle-slider"></div>
            </div>
        </div>
    </div>
    
    <div class="buttons">
        <button class="btn btn-primary" id="startBtn">🚀 开始自动化</button>
        <button class="btn btn-secondary" id="stopBtn">⏹️ 停止</button>
    </div>
    
    <div class="version">
        v1.0.0 - 全无焦点技术
    </div>
    
    <script src="popup.js"></script>
</body>
</html>
