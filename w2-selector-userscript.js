// ==UserScript==
// @name         TaxSlayer W-2 表单自动选择器 (全自动版)
// @namespace    http://tampermonkey.net/
// @version      1.1
// @description  自动检测页面并自动选择W-2表单，无需点击任何按钮
// <AUTHOR>
// @match        https://www.taxslayer.com/*
// @match        https://taxslayer.com/*
// @match        https://*.taxslayer.com/*
// @grant        none
// @run-at       document-idle
// ==/UserScript==

(function() {
    'use strict';
    
    console.log('🚀 TaxSlayer W-2 自动选择器已加载 (全自动版)');
    
    // 检查是否在Quick File页面
    function isQuickFilePage() {
        const pageTitle = document.querySelector('h1');
        return pageTitle && pageTitle.textContent === 'Quick File';
    }
    
    // 创建控制面板
    function createControlPanel() {
        // 避免重复创建
        if (document.getElementById('w2-auto-selector')) {
            return;
        }
        
        const panel = document.createElement('div');
        panel.id = 'w2-auto-selector';
        panel.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #2196F3;
            color: white;
            padding: 15px;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.3);
            z-index: 999999;
            font-family: Arial, sans-serif;
            font-size: 14px;
            min-width: 250px;
        `;
        
        panel.innerHTML = `
            <div style="margin-bottom: 10px; font-weight: bold;">
                ⚡ W-2 自动选择器
            </div>
            <div style="margin-bottom: 10px; color: #c8e6c9; font-size: 12px;">
                🚀 自动执行中...
            </div>
            <button id="w2-close-btn" style="
                background: #f44336;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 5px;
                cursor: pointer;
            ">关闭</button>
            <div id="w2-status" style="margin-top: 10px; font-size: 12px;"></div>
        `;
        
        document.body.appendChild(panel);

        // 绑定关闭事件
        document.getElementById('w2-close-btn').addEventListener('click', () => {
            panel.remove();
        });

        // 自动开始执行（2秒后）
        setTimeout(() => {
            updateStatus('开始自动执行 W-2 选择...');
            executeW2Selection();
        }, 2000);

        // 自动隐藏（30秒后）
        setTimeout(() => {
            if (panel.parentNode) {
                panel.style.opacity = '0.3';
            }
        }, 30000);
    }
    
    // 更新状态显示
    function updateStatus(message, isError = false) {
        const statusDiv = document.getElementById('w2-status');
        if (statusDiv) {
            statusDiv.textContent = message;
            statusDiv.style.color = isError ? '#ffcdd2' : '#c8e6c9';
        }
        console.log(isError ? '❌' : '✅', message);
    }
    
    // 确保窗口有焦点的函数
    async function ensureWindowFocus() {
        return new Promise((resolve) => {
            if (document.hasFocus()) {
                updateStatus('窗口已有焦点');
                resolve();
                return;
            }
            
            updateStatus('正在获取窗口焦点...');
            window.focus();
            
            const focusEvent = new MouseEvent('click', {
                view: window,
                bubbles: true,
                cancelable: true
            });
            document.body.dispatchEvent(focusEvent);
            
            setTimeout(() => {
                updateStatus(document.hasFocus() ? '成功获取焦点' : '继续执行（可能需要手动点击页面）');
                resolve();
            }, 500);
        });
    }
    
    // 模拟点击的函数
    function simulateClick(element) {
        const events = ['mousedown', 'mouseup', 'click'];
        events.forEach(eventType => {
            const event = new MouseEvent(eventType, {
                view: window,
                bubbles: true,
                cancelable: true
            });
            element.dispatchEvent(event);
        });
        console.log('🖱️ 已点击元素:', element.textContent || element.tagName);
    }
    
    // 检查W-2是否已选择
    function isW2Selected() {
        const selectedFormsList = document.querySelector('[role="list"]');
        if (selectedFormsList) {
            const w2Item = Array.from(selectedFormsList.querySelectorAll('*')).find(el => 
                el.textContent && el.textContent.includes('W-2')
            );
            if (w2Item) {
                return true;
            }
        }
        
        const noFormsText = Array.from(document.querySelectorAll('*')).find(el => 
            el.textContent && el.textContent.includes('No forms currently selected')
        );
        return !noFormsText;
    }
    
    // 主要的W-2选择函数
    async function executeW2Selection() {
        try {
            updateStatus('开始执行 W-2 选择...');
            
            // 检查页面
            if (!isQuickFilePage()) {
                updateStatus('错误：不在 Quick File 页面', true);
                return false;
            }
            
            // 确保窗口有焦点
            await ensureWindowFocus();
            
            // 检查是否已经选择了W-2
            if (isW2Selected()) {
                updateStatus('W-2 已选择，查找 Continue 按钮...');
                
                const continueBtn = Array.from(document.querySelectorAll('button')).find(btn => 
                    btn.textContent && btn.textContent.includes('Continue')
                );
                
                if (continueBtn) {
                    updateStatus('点击 Continue 按钮...');
                    simulateClick(continueBtn);
                    updateStatus('✅ 流程完成！');
                    return true;
                } else {
                    updateStatus('错误：未找到 Continue 按钮', true);
                    return false;
                }
            }
            
            // 查找搜索框
            updateStatus('查找搜索框...');
            const searchBox = document.querySelector('#autocomplete-input') || 
                             document.querySelector('textbox[name="Form search"]') ||
                             document.querySelector('input[type="text"]');
            
            if (!searchBox) {
                updateStatus('错误：未找到搜索框', true);
                return false;
            }
            
            // 激活搜索框
            updateStatus('激活搜索框...');
            searchBox.value = '';
            searchBox.focus();
            simulateClick(searchBox);
            
            // 等待下拉列表
            updateStatus('等待选项列表...');
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // 查找W-2选项
            const allOptions = document.querySelectorAll('[role="option"]');
            updateStatus(`找到 ${allOptions.length} 个选项`);
            
            let w2Option = null;
            for (let option of allOptions) {
                if (option.textContent && option.textContent.includes('W-2')) {
                    w2Option = option;
                    break;
                }
            }
            
            if (!w2Option) {
                updateStatus('错误：未找到 W-2 选项', true);
                return false;
            }
            
            // 点击W-2选项
            updateStatus('选择 W-2 表单...');
            simulateClick(w2Option);
            
            // 等待选择完成
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            // 验证并点击Continue
            if (isW2Selected()) {
                updateStatus('W-2 选择成功，查找 Continue 按钮...');
                
                const continueBtn = Array.from(document.querySelectorAll('button')).find(btn => 
                    btn.textContent && btn.textContent.includes('Continue')
                );
                
                if (continueBtn) {
                    updateStatus('点击 Continue 按钮...');
                    simulateClick(continueBtn);
                    updateStatus('🎉 W-2 选择流程完成！');
                    
                    // 3秒后自动关闭面板
                    setTimeout(() => {
                        const panel = document.getElementById('w2-auto-selector');
                        if (panel) panel.remove();
                    }, 3000);
                    
                    return true;
                } else {
                    updateStatus('错误：未找到 Continue 按钮', true);
                    return false;
                }
            } else {
                updateStatus('错误：W-2 选择失败', true);
                return false;
            }
            
        } catch (error) {
            updateStatus(`错误：${error.message}`, true);
            console.error('❌ 执行错误:', error);
            return false;
        }
    }
    
    // 页面加载完成后检查并显示控制面板
    function init() {
        // 等待页面完全加载
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', init);
            return;
        }
        
        // 检查是否在目标页面
        if (isQuickFilePage()) {
            console.log('✅ 检测到 Quick File 页面，显示控制面板并准备自动执行');
            createControlPanel();
        }
    }
    
    // 监听页面变化（SPA应用）
    let lastUrl = location.href;
    new MutationObserver(() => {
        const url = location.href;
        if (url !== lastUrl) {
            lastUrl = url;
            setTimeout(init, 1000); // 延迟检查新页面
        }
    }).observe(document, { subtree: true, childList: true });
    
    // 初始化
    init();
    
})();
