// TaxSlayer 自动填写助手 - 后台脚本
console.log('🚀 TaxSlayer 自动填写助手后台服务启动');

// 扩展安装时的初始化
chrome.runtime.onInstalled.addListener((details) => {
    console.log('📦 扩展已安装/更新:', details.reason);
    
    // 设置默认配置
    chrome.storage.local.set({
        autoMode: true,
        debugMode: true,
        w2AutoSelect: true,
        addressAutoFill: true,
        taxpayerAutoFill: true
    });
});

// 监听来自content script的消息
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    console.log('📨 收到消息:', request);
    
    switch (request.action) {
        case 'getConfig':
            // 获取配置
            chrome.storage.local.get(null, (config) => {
                sendResponse(config);
            });
            return true; // 保持消息通道开放
            
        case 'saveConfig':
            // 保存配置
            chrome.storage.local.set(request.config, () => {
                sendResponse({ success: true });
            });
            return true;
            
        case 'log':
            // 记录日志
            console.log(`[${request.level}] ${request.message}`);
            break;
            
        case 'notification':
            // 显示通知
            chrome.notifications.create({
                type: 'basic',
                iconUrl: 'icons/icon48.png',
                title: 'TaxSlayer 助手',
                message: request.message
            });
            break;
            
        case 'updateBadge':
            // 更新徽章
            chrome.action.setBadgeText({
                text: request.text,
                tabId: sender.tab.id
            });
            chrome.action.setBadgeBackgroundColor({
                color: request.color || '#4CAF50',
                tabId: sender.tab.id
            });
            break;
    }
});

// 监听标签页更新
chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
    if (changeInfo.status === 'complete' && tab.url && tab.url.includes('taxslayer.com')) {
        console.log('🌐 TaxSlayer页面加载完成:', tab.url);
        
        // 重置徽章
        chrome.action.setBadgeText({
            text: '',
            tabId: tabId
        });
    }
});

// 监听标签页激活
chrome.tabs.onActivated.addListener((activeInfo) => {
    chrome.tabs.get(activeInfo.tabId, (tab) => {
        if (tab.url && tab.url.includes('taxslayer.com')) {
            console.log('🎯 切换到TaxSlayer标签页');
        }
    });
});

// 扩展启动时的初始化
chrome.runtime.onStartup.addListener(() => {
    console.log('🔄 浏览器启动，扩展服务重新初始化');
});
