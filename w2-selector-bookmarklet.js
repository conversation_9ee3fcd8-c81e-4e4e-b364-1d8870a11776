// W-2 选择器书签脚本
// 将下面的代码复制并创建为浏览器书签，点击即可使用

javascript:(function(){
    if(document.getElementById('w2-auto-selector')){
        document.getElementById('w2-auto-selector').remove();
    }
    
    const panel = document.createElement('div');
    panel.id = 'w2-auto-selector';
    panel.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #2196F3;
        color: white;
        padding: 15px;
        border-radius: 10px;
        box-shadow: 0 4px 8px rgba(0,0,0,0.3);
        z-index: 999999;
        font-family: Arial, sans-serif;
        font-size: 14px;
        min-width: 250px;
    `;
    
    panel.innerHTML = `
        <div style="margin-bottom: 10px; font-weight: bold;">
            🎯 W-2 自动选择器
        </div>
        <button id="w2-auto-btn" style="
            background: #4CAF50;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 5px;
            cursor: pointer;
            margin-right: 10px;
        ">开始选择 W-2</button>
        <button id="w2-close-btn" style="
            background: #f44336;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 5px;
            cursor: pointer;
        ">关闭</button>
        <div id="w2-status" style="margin-top: 10px; font-size: 12px;"></div>
    `;
    
    document.body.appendChild(panel);
    
    function updateStatus(message, isError = false) {
        const statusDiv = document.getElementById('w2-status');
        if (statusDiv) {
            statusDiv.textContent = message;
            statusDiv.style.color = isError ? '#ffcdd2' : '#c8e6c9';
        }
        console.log(isError ? '❌' : '✅', message);
    }
    
    function simulateClick(element) {
        const events = ['mousedown', 'mouseup', 'click'];
        events.forEach(eventType => {
            const event = new MouseEvent(eventType, {
                view: window,
                bubbles: true,
                cancelable: true
            });
            element.dispatchEvent(event);
        });
    }
    
    function isW2Selected() {
        const selectedFormsList = document.querySelector('[role="list"]');
        if (selectedFormsList) {
            const w2Item = Array.from(selectedFormsList.querySelectorAll('*')).find(el => 
                el.textContent && el.textContent.includes('W-2')
            );
            if (w2Item) return true;
        }
        
        const noFormsText = Array.from(document.querySelectorAll('*')).find(el => 
            el.textContent && el.textContent.includes('No forms currently selected')
        );
        return !noFormsText;
    }
    
    async function executeW2Selection() {
        try {
            updateStatus('开始执行 W-2 选择...');
            
            const pageTitle = document.querySelector('h1');
            if (!pageTitle || pageTitle.textContent !== 'Quick File') {
                updateStatus('错误：不在 Quick File 页面', true);
                return false;
            }
            
            if (isW2Selected()) {
                updateStatus('W-2 已选择，查找 Continue 按钮...');
                const continueBtn = Array.from(document.querySelectorAll('button')).find(btn => 
                    btn.textContent && btn.textContent.includes('Continue')
                );
                
                if (continueBtn) {
                    updateStatus('点击 Continue 按钮...');
                    simulateClick(continueBtn);
                    updateStatus('✅ 流程完成！');
                    return true;
                }
            }
            
            updateStatus('查找搜索框...');
            const searchBox = document.querySelector('#autocomplete-input') || 
                             document.querySelector('input[type="text"]');
            
            if (!searchBox) {
                updateStatus('错误：未找到搜索框', true);
                return false;
            }
            
            updateStatus('激活搜索框...');
            searchBox.value = '';
            searchBox.focus();
            simulateClick(searchBox);
            
            updateStatus('等待选项列表...');
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            const allOptions = document.querySelectorAll('[role="option"]');
            updateStatus(`找到 ${allOptions.length} 个选项`);
            
            let w2Option = null;
            for (let option of allOptions) {
                if (option.textContent && option.textContent.includes('W-2')) {
                    w2Option = option;
                    break;
                }
            }
            
            if (!w2Option) {
                updateStatus('错误：未找到 W-2 选项', true);
                return false;
            }
            
            updateStatus('选择 W-2 表单...');
            simulateClick(w2Option);
            
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            if (isW2Selected()) {
                updateStatus('W-2 选择成功，查找 Continue 按钮...');
                const continueBtn = Array.from(document.querySelectorAll('button')).find(btn => 
                    btn.textContent && btn.textContent.includes('Continue')
                );
                
                if (continueBtn) {
                    updateStatus('点击 Continue 按钮...');
                    simulateClick(continueBtn);
                    updateStatus('🎉 W-2 选择流程完成！');
                    setTimeout(() => {
                        const panel = document.getElementById('w2-auto-selector');
                        if (panel) panel.remove();
                    }, 3000);
                    return true;
                }
            }
            
            updateStatus('错误：W-2 选择失败', true);
            return false;
            
        } catch (error) {
            updateStatus(`错误：${error.message}`, true);
            return false;
        }
    }
    
    document.getElementById('w2-auto-btn').addEventListener('click', executeW2Selection);
    document.getElementById('w2-close-btn').addEventListener('click', () => {
        panel.remove();
    });
    
    updateStatus('脚本已加载，点击按钮开始');
})();
