// W-2 选择器 - 静默自动执行版本
// 将下面的代码复制并创建为浏览器书签，点击即可静默自动执行（仅控制台输出）

javascript:(function(){
    console.log('🚀 W-2静默自动选择器启动');
    
    // 模拟点击函数
    function simulateClick(element) {
        const events = ['mousedown', 'mouseup', 'click'];
        events.forEach(eventType => {
            const event = new MouseEvent(eventType, {
                view: window,
                bubbles: true,
                cancelable: true
            });
            element.dispatchEvent(event);
        });
    }
    
    // 检查W-2是否已选择
    function isW2Selected() {
        const selectedFormsList = document.querySelector('[role="list"]');
        if (selectedFormsList) {
            const w2Item = Array.from(selectedFormsList.querySelectorAll('*')).find(el => 
                el.textContent && el.textContent.includes('W-2')
            );
            if (w2Item) return true;
        }
        
        const noFormsText = Array.from(document.querySelectorAll('*')).find(el => 
            el.textContent && el.textContent.includes('No forms currently selected')
        );
        return !noFormsText;
    }
    
    // 主要执行函数
    async function executeW2Selection() {
        try {
            console.log('✅ 检查当前页面...');
            
            const pageTitle = document.querySelector('h1');
            if (!pageTitle || pageTitle.textContent !== 'Quick File') {
                console.log('❌ 错误：不在 Quick File 页面');
                return false;
            }
            
            // 检查是否已经选择了W-2
            if (isW2Selected()) {
                console.log('✅ W-2 已选择，查找 Continue 按钮...');
                const continueBtn = Array.from(document.querySelectorAll('button')).find(btn => 
                    btn.textContent && btn.textContent.includes('Continue')
                );
                
                if (continueBtn) {
                    console.log('✅ 点击 Continue 按钮...');
                    simulateClick(continueBtn);
                    console.log('🎉 流程完成！页面即将跳转...');
                    return true;
                } else {
                    console.log('❌ 错误：未找到 Continue 按钮');
                    return false;
                }
            }
            
            console.log('✅ 查找搜索框...');
            const searchBox = document.querySelector('#autocomplete-input') || 
                             document.querySelector('input[type="text"]');
            
            if (!searchBox) {
                console.log('❌ 错误：未找到搜索框');
                return false;
            }
            
            console.log('✅ 激活搜索框...');
            searchBox.value = '';
            searchBox.focus();
            simulateClick(searchBox);
            
            console.log('⏳ 等待选项列表出现...');
            await new Promise(resolve => setTimeout(resolve, 1500));
            
            const allOptions = document.querySelectorAll('[role="option"]');
            console.log(`✅ 找到 ${allOptions.length} 个选项，查找 W-2...`);
            
            let w2Option = null;
            for (let option of allOptions) {
                if (option.textContent && option.textContent.includes('W-2')) {
                    w2Option = option;
                    break;
                }
            }
            
            if (!w2Option) {
                console.log('❌ 错误：未找到 W-2 选项');
                console.log('可用选项:', Array.from(allOptions).map(opt => opt.textContent));
                return false;
            }
            
            console.log('✅ 找到 W-2 选项，正在选择...');
            simulateClick(w2Option);
            
            console.log('⏳ 等待选择完成...');
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            // 验证选择结果
            if (isW2Selected()) {
                console.log('✅ W-2 选择成功！查找 Continue 按钮...');
                const continueBtn = Array.from(document.querySelectorAll('button')).find(btn => 
                    btn.textContent && btn.textContent.includes('Continue')
                );
                
                if (continueBtn) {
                    console.log('✅ 点击 Continue 按钮...');
                    simulateClick(continueBtn);
                    console.log('🎉 W-2 选择流程完成！页面即将跳转...');
                    return true;
                } else {
                    console.log('❌ 错误：选择成功但未找到 Continue 按钮');
                    return false;
                }
            } else {
                console.log('❌ 错误：W-2 选择失败');
                return false;
            }
            
        } catch (error) {
            console.error('❌ 执行错误:', error);
            return false;
        }
    }
    
    // 立即开始自动执行
    console.log('⚡ 开始自动执行 W-2 选择...');
    setTimeout(() => {
        executeW2Selection().then(success => {
            if (success) {
                console.log('🎉 脚本执行成功！');
            } else {
                console.log('❌ 脚本执行失败，请检查页面状态');
            }
        });
    }, 500);
    
    return '🚀 W-2静默自动选择器已启动，请查看控制台日志';
})();
