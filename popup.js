// TaxSlayer 自动填写助手 - 弹窗脚本
console.log('🎮 弹窗界面加载');

let config = {
    autoMode: true,
    debugMode: true,
    w2AutoSelect: true,
    addressAutoFill: true,
    taxpayerAutoFill: true
};

// 初始化弹窗
document.addEventListener('DOMContentLoaded', async () => {
    console.log('📋 初始化弹窗界面');
    
    // 加载配置
    await loadConfig();
    
    // 检查当前页面状态
    await checkPageStatus();
    
    // 绑定事件
    bindEvents();
    
    // 更新界面
    updateUI();
});

// 加载配置
async function loadConfig() {
    try {
        const result = await chrome.runtime.sendMessage({ action: 'getConfig' });
        if (result) {
            config = { ...config, ...result };
            console.log('✅ 配置加载完成:', config);
        }
    } catch (error) {
        console.error('❌ 配置加载失败:', error);
    }
}

// 保存配置
async function saveConfig() {
    try {
        await chrome.runtime.sendMessage({ 
            action: 'saveConfig', 
            config: config 
        });
        console.log('✅ 配置保存完成');
    } catch (error) {
        console.error('❌ 配置保存失败:', error);
    }
}

// 检查页面状态
async function checkPageStatus() {
    try {
        const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
        const statusElement = document.getElementById('statusText');
        
        if (tab.url && tab.url.includes('taxslayer.com')) {
            statusElement.textContent = '✅ TaxSlayer 页面已检测';
            document.getElementById('status').className = 'status active';
        } else {
            statusElement.textContent = '❌ 请访问 TaxSlayer 网站';
            document.getElementById('status').className = 'status inactive';
        }
    } catch (error) {
        console.error('❌ 页面状态检查失败:', error);
        document.getElementById('statusText').textContent = '❌ 状态检查失败';
        document.getElementById('status').className = 'status inactive';
    }
}

// 绑定事件
function bindEvents() {
    // 切换开关事件
    const toggles = [
        { id: 'w2Toggle', key: 'w2AutoSelect' },
        { id: 'addressToggle', key: 'addressAutoFill' },
        { id: 'taxpayerToggle', key: 'taxpayerAutoFill' },
        { id: 'autoToggle', key: 'autoMode' },
        { id: 'debugToggle', key: 'debugMode' }
    ];
    
    toggles.forEach(({ id, key }) => {
        const element = document.getElementById(id);
        element.addEventListener('click', () => {
            config[key] = !config[key];
            updateToggle(element, config[key]);
            saveConfig();
        });
    });
    
    // 开始按钮
    document.getElementById('startBtn').addEventListener('click', async () => {
        console.log('🚀 手动启动自动化');
        await sendMessageToContent({ action: 'startAutomation' });
        window.close();
    });
    
    // 停止按钮
    document.getElementById('stopBtn').addEventListener('click', async () => {
        console.log('⏹️ 停止自动化');
        await sendMessageToContent({ action: 'stopAutomation' });
        window.close();
    });
}

// 更新界面
function updateUI() {
    // 更新所有切换开关
    updateToggle(document.getElementById('w2Toggle'), config.w2AutoSelect);
    updateToggle(document.getElementById('addressToggle'), config.addressAutoFill);
    updateToggle(document.getElementById('taxpayerToggle'), config.taxpayerAutoFill);
    updateToggle(document.getElementById('autoToggle'), config.autoMode);
    updateToggle(document.getElementById('debugToggle'), config.debugMode);
}

// 更新切换开关状态
function updateToggle(element, isActive) {
    if (isActive) {
        element.classList.add('active');
    } else {
        element.classList.remove('active');
    }
}

// 发送消息到content script
async function sendMessageToContent(message) {
    try {
        const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
        if (tab.url && tab.url.includes('taxslayer.com')) {
            await chrome.tabs.sendMessage(tab.id, message);
            console.log('📤 消息已发送到content script:', message);
        } else {
            console.warn('⚠️ 当前页面不是TaxSlayer网站');
        }
    } catch (error) {
        console.error('❌ 发送消息失败:', error);
    }
}
